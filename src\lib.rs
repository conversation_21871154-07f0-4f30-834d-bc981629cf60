// Библиотека для работы с BLE терминалом и парсинга ANSI цветных кодов

use colored::*;
use regex::Regex;

// Функция для обработки и вывода цветного текста с ANSI кодами
pub fn print_colored_string(bytes: &[u8]) {
    // Преобразуем байты в строку, сохраняя ANSI escape последовательности
    let text = String::from_utf8_lossy(bytes);
    
    // Регулярное выражение для поиска ANSI escape последовательностей
    let ansi_regex = Regex::new(r"\x1B\[([0-9;]*)m").unwrap();
    
    let mut last_end = 0;
    let mut current_color: Option<Color> = None;
    
    for mat in ansi_regex.find_iter(&text) {
        // Выводим текст до ANSI кода
        let before_ansi = &text[last_end..mat.start()];
        if !before_ansi.is_empty() {
            if let Some(color) = current_color {
                print!("{}", before_ansi.color(color));
            } else {
                print!("{}", before_ansi);
            }
        }
        
        // Парсим ANSI код
        let ansi_code = &text[mat.start()..mat.end()];
        current_color = parse_ansi_color(ansi_code);
        
        last_end = mat.end();
    }
    
    // Выводим оставшийся текст
    let remaining = &text[last_end..];
    if !remaining.is_empty() {
        if let Some(color) = current_color {
            print!("{}", remaining.color(color));
        } else {
            print!("{}", remaining);
        }
    }
    
    // Добавляем перенос строки, если его нет
    if !text.ends_with('\n') {
        println!();
    }
}

// Функция для парсинга ANSI цветных кодов
pub fn parse_ansi_color(ansi_code: &str) -> Option<Color> {
    // Извлекаем числовые коды из ANSI последовательности
    let codes_str = ansi_code.trim_start_matches("\x1B[").trim_end_matches('m');
    
    if codes_str.is_empty() || codes_str == "0" {
        return None; // Сброс цвета
    }
    
    // Парсим коды, разделенные точкой с запятой
    let codes: Vec<u8> = codes_str
        .split(';')
        .filter_map(|s| s.parse().ok())
        .collect();
    
    // Ищем цветовые коды в последовательности
    for &code in &codes {
        match code {
            // Стандартные цвета (30-37)
            30 => return Some(Color::Black),
            31 => return Some(Color::Red),
            32 => return Some(Color::Green),
            33 => return Some(Color::Yellow),
            34 => return Some(Color::Blue),
            35 => return Some(Color::Magenta),
            36 => return Some(Color::Cyan),
            37 => return Some(Color::White),
            
            // Яркие цвета (90-97)
            90 => return Some(Color::BrightBlack),
            91 => return Some(Color::BrightRed),
            92 => return Some(Color::BrightGreen),
            93 => return Some(Color::BrightYellow),
            94 => return Some(Color::BrightBlue),
            95 => return Some(Color::BrightMagenta),
            96 => return Some(Color::BrightCyan),
            97 => return Some(Color::BrightWhite),
            
            // Игнорируем другие коды (жирный, курсив и т.д.)
            _ => continue,
        }
    }
    
    None
}
