use btleplug::api::{Central, Manager as _, Peripheral as _, ScanFilter};
use btleplug::platform::Manager;
use futures::stream::StreamExt;
use std::error::Error;
use std::io::{self, Write};
use std::time::Duration;
use tokio::time;
use uuid::Uuid;

// UUID сервиса и характеристики
const SERVICE_UUID: Uuid = Uuid::from_u128(0x000000F3_0000_1000_8000_00805f9b34fb);
const CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F308_0000_1000_8000_00805f9b34fb);

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    println!("BLE Terminal - поиск устройств...");

    // Получаем менеджер BLE
    let manager = Manager::new().await?;
    let adapters = manager.adapters().await?;

    if adapters.is_empty() {
        eprintln!("Не найдено BLE адаптеров");
        return Ok(());
    }

    let central = &adapters[0];

    // Начинаем сканирование
    central.start_scan(ScanFilter::default()).await?;

    println!("Сканирование в течение 10 секунд...");
    time::sleep(Duration::from_secs(10)).await;

    // Останавливаем сканирование
    central.stop_scan().await?;

    // Получаем найденные устройства
    let peripherals = central.peripherals().await?;

    if peripherals.is_empty() {
        println!("Устройства не найдены");
        return Ok(());
    }

    // Показываем список устройств
    println!("\nНайденные устройства:");
    for (i, peripheral) in peripherals.iter().enumerate() {
        let properties = peripheral.properties().await?;
        let name = properties
            .unwrap_or_default()
            .local_name
            .unwrap_or_else(|| "Неизвестное устройство".to_string());
        println!("{}. {} ({})", i + 1, name, peripheral.address());
    }

    // Запрашиваем выбор пользователя
    print!("\nВыберите устройство (введите номер): ");
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;

    let choice: usize = input.trim().parse().unwrap_or(0);

    if choice == 0 || choice > peripherals.len() {
        println!("Неверный выбор");
        return Ok(());
    }

    let peripheral = &peripherals[choice - 1];
    let properties = peripheral.properties().await?;
    let device_name = properties
        .unwrap_or_default()
        .local_name
        .unwrap_or_else(|| "Неизвестное устройство".to_string());

    println!("Подключение к устройству: {}", device_name);

    // Подключаемся к устройству
    peripheral.connect().await?;
    println!("Подключено!");

    // Обнаруживаем сервисы
    peripheral.discover_services().await?;

    // Ищем нужный сервис
    let services = peripheral.services();
    let service = services
        .iter()
        .find(|s| s.uuid == SERVICE_UUID)
        .ok_or("Сервис не найден")?;

    println!("Найден сервис: {}", service.uuid);

    // Ищем нужную характеристику
    let characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == CHARACTERISTIC_UUID)
        .ok_or("Характеристика не найдена")?;

    println!("Найдена характеристика: {}", characteristic.uuid);

    // Подписываемся на уведомления
    peripheral.subscribe(characteristic).await?;
    println!("Подписка на уведомления активирована");

    println!("\nОжидание данных (нажмите Ctrl+C для выхода):");
    println!("----------------------------------------");

    // Слушаем уведомления
    let mut notification_stream = peripheral.notifications().await?;

    while let Some(data) = notification_stream.next().await {
        if data.uuid == CHARACTERISTIC_UUID {
            // Выводим данные в разных форматах
            println!("Получены данные:");
            println!("  Hex: {}", hex_string(&data.value));
            println!("  ASCII: {}", ascii_string(&data.value));
            println!("  Bytes: {:?}", data.value);
            println!("----------------------------------------");
        }
    }

    Ok(())
}

// Функция для преобразования байтов в hex строку
fn hex_string(bytes: &[u8]) -> String {
    bytes
        .iter()
        .map(|b| format!("{:02x}", b))
        .collect::<Vec<_>>()
        .join(" ")
}

// Функция для преобразования байтов в ASCII строку
fn ascii_string(bytes: &[u8]) -> String {
    bytes
        .iter()
        .map(|&b| {
            if b.is_ascii_graphic() || b == b' ' {
                b as char
            } else {
                '.'
            }
        })
        .collect()
}
