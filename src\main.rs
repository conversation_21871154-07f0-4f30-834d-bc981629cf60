use btleplug::api::{<PERSON>, Manager as _, Peripheral as _, ScanFilter};
use btleplug::platform::Manager;
use colored::*;
use futures::stream::StreamExt;
use regex::Regex;
use std::error::Error;
use std::io::{self, Write};
use std::time::Duration;
use tokio::time;
use uuid::Uuid;

#[cfg(test)]
mod test_ansi;

// UUID сервиса и характеристики
const SERVICE_UUID: Uuid = Uuid::from_u128(0x000000F3_0000_1000_8000_00805f9b34fb);
const CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F307_0000_1000_8000_00805f9b34fb);

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    println!("BLE Terminal - поиск устройств...");

    let manager = Manager::new().await?;
    let adapters = manager.adapters().await?;

    if adapters.is_empty() {
        eprintln!("Не найдено BLE адаптеров");
        return Ok(());
    }

    let central = &adapters[0];

    central.start_scan(ScanFilter::default()).await?;

    println!("Сканирование в течение 10 секунд...");
    time::sleep(Duration::from_secs(10)).await;

    central.stop_scan().await?;

    let peripherals = central.peripherals().await?;

    if peripherals.is_empty() {
        println!("Устройства не найдены");
        return Ok(());
    }

    println!("\nНайденные устройства:");
    for (i, peripheral) in peripherals.iter().enumerate() {
        let properties = peripheral.properties().await?;
        let name = properties
            .unwrap_or_default()
            .local_name
            .unwrap_or_else(|| "Неизвестное устройство".to_string());
        println!("{}. {} ({})", i + 1, name, peripheral.address());
    }

    print!("\nВыберите устройство (введите номер): ");
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;

    let choice: usize = input.trim().parse().unwrap_or(0);

    if choice == 0 || choice > peripherals.len() {
        println!("Неверный выбор");
        return Ok(());
    }

    let peripheral = &peripherals[choice - 1];
    let properties = peripheral.properties().await?;
    let device_name = properties
        .unwrap_or_default()
        .local_name
        .unwrap_or_else(|| "Неизвестное устройство".to_string());

    println!("Подключение к устройству: {}", device_name);

    peripheral.connect().await?;
    println!("Подключено!");

    peripheral.discover_services().await?;

    let services = peripheral.services();
    let service = services
        .iter()
        .find(|s| s.uuid == SERVICE_UUID)
        .ok_or("Сервис не найден")?;

    println!("Найден сервис: {}", service.uuid);

    let characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == CHARACTERISTIC_UUID)
        .ok_or("Характеристика не найдена")?;

    println!("Найдена характеристика: {}", characteristic.uuid);

    peripheral.subscribe(characteristic).await?;
    println!("Подписка на уведомления активирована");

    println!("\nОжидание данных (нажмите Ctrl+C для выхода):");
    println!("----------------------------------------");

    // Тестируем парсер ANSI кодов с примером
    println!("Тест парсера ANSI кодов:");
    let test_data = b".\x1B[0;32mI (170974) -queues hlp-: sending uart cmd: == heartbeat ==\x1B[0m.";
    print_colored_string(test_data);
    println!("----------------------------------------");

    let mut notification_stream = peripheral.notifications().await?;

    while let Some(data) = notification_stream.next().await {
        if data.uuid == CHARACTERISTIC_UUID {
            // println!("Получены данные:");
            // println!("  Hex: {}", hex_string(&data.value));
            print_colored_string(&data.value);
            // println!("  Bytes: {:?}", data.value);
            // println!("----------------------------------------");
        }
    }

    Ok(())
}

// Функция для преобразования байтов в hex строку
fn hex_string(bytes: &[u8]) -> String {
    bytes
        .iter()
        .map(|b| format!("{:02x}", b))
        .collect::<Vec<_>>()
        .join(" ")
}

// Функция для преобразования байтов в ASCII строку
fn ascii_string(bytes: &[u8]) -> String {
    bytes
        .iter()
        .map(|&b| {
            if b.is_ascii_graphic() || b == b' ' {
                b as char
            } else {
                '.'
            }
        })
        .collect()
}

// Функция для обработки и вывода цветного текста с ANSI кодами
pub fn print_colored_string(bytes: &[u8]) {
    // Преобразуем байты в строку, сохраняя ANSI escape последовательности
    let text = String::from_utf8_lossy(bytes);

    // Регулярное выражение для поиска ANSI escape последовательностей
    let ansi_regex = Regex::new(r"\x1B\[([0-9;]*)m").unwrap();

    let mut last_end = 0;
    let mut current_color: Option<Color> = None;

    for mat in ansi_regex.find_iter(&text) {
        // Выводим текст до ANSI кода
        let before_ansi = &text[last_end..mat.start()];
        if !before_ansi.is_empty() {
            if let Some(color) = current_color {
                print!("{}", before_ansi.color(color));
            } else {
                print!("{}", before_ansi);
            }
        }

        // Парсим ANSI код
        let ansi_code = &text[mat.start()..mat.end()];
        current_color = parse_ansi_color(ansi_code);

        last_end = mat.end();
    }

    // Выводим оставшийся текст
    let remaining = &text[last_end..];
    if !remaining.is_empty() {
        if let Some(color) = current_color {
            print!("{}", remaining.color(color));
        } else {
            print!("{}", remaining);
        }
    }

    // Добавляем перенос строки, если его нет
    if !text.ends_with('\n') {
        println!();
    }
}

// Функция для парсинга ANSI цветных кодов
pub fn parse_ansi_color(ansi_code: &str) -> Option<Color> {
    // Извлекаем числовые коды из ANSI последовательности
    let codes_str = ansi_code.trim_start_matches("\x1B[").trim_end_matches('m');

    if codes_str.is_empty() || codes_str == "0" {
        return None; // Сброс цвета
    }

    // Парсим коды, разделенные точкой с запятой
    let codes: Vec<u8> = codes_str
        .split(';')
        .filter_map(|s| s.parse().ok())
        .collect();

    // Ищем цветовые коды в последовательности
    for &code in &codes {
        match code {
            // Стандартные цвета (30-37)
            30 => return Some(Color::Black),
            31 => return Some(Color::Red),
            32 => return Some(Color::Green),
            33 => return Some(Color::Yellow),
            34 => return Some(Color::Blue),
            35 => return Some(Color::Magenta),
            36 => return Some(Color::Cyan),
            37 => return Some(Color::White),

            // Яркие цвета (90-97)
            90 => return Some(Color::BrightBlack),
            91 => return Some(Color::BrightRed),
            92 => return Some(Color::BrightGreen),
            93 => return Some(Color::BrightYellow),
            94 => return Some(Color::BrightBlue),
            95 => return Some(Color::BrightMagenta),
            96 => return Some(Color::BrightCyan),
            97 => return Some(Color::BrightWhite),

            // Игнорируем другие коды (жирный, курсив и т.д.)
            _ => continue,
        }
    }

    None
}
