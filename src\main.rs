use btleplug::api::{<PERSON>, Manager as _, Peripheral as _, <PERSON>an<PERSON><PERSON>er, WriteType};
use btleplug::platform::Manager;
use colored::*;
use crossterm::{
    cursor,
    event::{self, Event, KeyCode, KeyEvent, KeyModifiers},
    execute,
    terminal::{self, ClearType},
};
use futures::stream::StreamExt;
use regex::Regex;
use std::error::Error;
use std::io::{self, Write};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc;
use tokio::time;
use uuid::Uuid;

#[cfg(test)]
mod test_ansi;

// UUID сервиса и характеристики
const SERVICE_UUID: Uuid = Uuid::from_u128(0x000000F3_0000_1000_8000_00805f9b34fb);
const CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F307_0000_1000_8000_00805f9b34fb);
const WRITE_CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F308_0000_1000_8000_00805f9b34fb);

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    println!("BLE Terminal - поиск устройств...");

    let manager = Manager::new().await?;
    let adapters = manager.adapters().await?;

    if adapters.is_empty() {
        eprintln!("Не найдено BLE адаптеров");
        return Ok(());
    }

    let central = &adapters[0];

    central.start_scan(ScanFilter::default()).await?;

    println!("Сканирование в течение 10 секунд...");
    time::sleep(Duration::from_secs(10)).await;

    central.stop_scan().await?;

    let peripherals = central.peripherals().await?;

    if peripherals.is_empty() {
        println!("Устройства не найдены");
        return Ok(());
    }

    println!("\nНайденные устройства:");
    for (i, peripheral) in peripherals.iter().enumerate() {
        let properties = peripheral.properties().await?;
        let name = properties
            .unwrap_or_default()
            .local_name
            .unwrap_or_else(|| "Неизвестное устройство".to_string());
        println!("{}. {} ({})", i + 1, name, peripheral.address());
    }

    print!("\nВыберите устройство (введите номер): ");
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;

    let choice: usize = input.trim().parse().unwrap_or(0);

    if choice == 0 || choice > peripherals.len() {
        println!("Неверный выбор");
        return Ok(());
    }

    let peripheral = peripherals[choice - 1].clone();
    let properties = peripheral.properties().await?;
    let device_name = properties
        .unwrap_or_default()
        .local_name
        .unwrap_or_else(|| "Неизвестное устройство".to_string());

    println!("Подключение к устройству: {}", device_name);

    peripheral.connect().await?;
    println!("Подключено!");

    peripheral.discover_services().await?;

    let services = peripheral.services();
    let service = services
        .iter()
        .find(|s| s.uuid == SERVICE_UUID)
        .ok_or("Сервис не найден")?;

    println!("Найден сервис: {}", service.uuid);

    let read_characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == CHARACTERISTIC_UUID)
        .ok_or("Характеристика для чтения не найдена")?;

    let write_characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == WRITE_CHARACTERISTIC_UUID)
        .ok_or("Характеристика для записи не найдена")?;

    println!("Найдена характеристика для чтения: {}", read_characteristic.uuid);
    println!("Найдена характеристика для записи: {}", write_characteristic.uuid);

    peripheral.subscribe(read_characteristic).await?;
    println!("Подписка на уведомления активирована");

    println!("\nТерминал готов. Введите команды для отправки или нажмите Ctrl+C для выхода:");
    println!("Команды отправляются после нажатия Enter. Логи устройства будут показаны выше.");
    println!("----------------------------------------");

    // Включаем raw mode для обработки клавиш
    terminal::enable_raw_mode()?;

    // Создаем Arc для совместного использования peripheral между задачами
    let peripheral = Arc::new(peripheral);
    let write_char = Arc::new(write_characteristic.clone());

    // Каналы для передачи данных между задачами
    let (tx_log, mut rx_log) = mpsc::unbounded_channel::<Vec<u8>>();
    let (tx_command, mut rx_command) = mpsc::unbounded_channel::<String>();

    // Задача для чтения уведомлений
    let peripheral_read = Arc::clone(&peripheral);
    let notification_task = tokio::spawn(async move {
        let mut notification_stream = peripheral_read.notifications().await.unwrap();

        while let Some(data) = notification_stream.next().await {
            if data.uuid == CHARACTERISTIC_UUID {
                let _ = tx_log.send(data.value);
            }
        }
    });

    // Задача для отправки команд
    let peripheral_write = Arc::clone(&peripheral);
    let write_char_clone = Arc::clone(&write_char);
    let send_task = tokio::spawn(async move {
        while let Some(command) = rx_command.recv().await {
            let data = format!("{}\n", command);
            if let Err(e) = peripheral_write.write(&write_char_clone, data.as_bytes(), WriteType::WithoutResponse).await {
                eprintln!("Ошибка отправки команды: {}", e);
            }
        }
    });

    // Основная задача для управления интерфейсом
    let interface_task = tokio::spawn(async move {
        let mut current_input = String::new();
        let mut cursor_pos = 0;

        // Показываем начальный промпт
        print!("> ");
        io::stdout().flush().unwrap();

        loop {
            tokio::select! {
                // Получили данные от устройства
                Some(data) = rx_log.recv() => {
                    // Сохраняем текущую позицию курсора
                    execute!(io::stdout(), cursor::SavePosition).unwrap();

                    // Очищаем текущую строку
                    execute!(io::stdout(),
                        cursor::MoveToColumn(0),
                        terminal::Clear(ClearType::CurrentLine)
                    ).unwrap();

                    // Выводим данные от устройства
                    print_colored_string(&data);

                    // Восстанавливаем строку ввода
                    print!("> {}", current_input);

                    // Устанавливаем курсор в правильную позицию
                    if cursor_pos < current_input.len() {
                        let chars_to_move = current_input.chars().count() - cursor_pos;
                        execute!(io::stdout(), cursor::MoveLeft(chars_to_move as u16)).unwrap();
                    }

                    io::stdout().flush().unwrap();
                }

                // Обработка ввода с клавиатуры
                _ = tokio::time::sleep(Duration::from_millis(50)) => {
                    if event::poll(Duration::from_millis(0)).unwrap() {
                        if let Ok(Event::Key(key_event)) = event::read() {
                            match key_event {
                                KeyEvent {
                                    code: KeyCode::Char('c'),
                                    modifiers: KeyModifiers::CONTROL,
                                    ..
                                } => {
                                    // Ctrl+C - выход
                                    break;
                                }
                                KeyEvent {
                                    code: KeyCode::Enter,
                                    ..
                                } => {
                                    // Enter - отправить команду
                                    if !current_input.trim().is_empty() {
                                        let _ = tx_command.send(current_input.trim().to_string());

                                        // Очищаем строку и показываем что отправили
                                        execute!(io::stdout(),
                                            cursor::MoveToColumn(0),
                                            terminal::Clear(ClearType::CurrentLine)
                                        ).unwrap();
                                        println!("Отправлено: {}", current_input.trim());

                                        current_input.clear();
                                        cursor_pos = 0;
                                    }

                                    // Показываем новый промпт
                                    print!("> ");
                                    io::stdout().flush().unwrap();
                                }
                                KeyEvent {
                                    code: KeyCode::Backspace,
                                    ..
                                } => {
                                    // Backspace - удалить последний символ
                                    if !current_input.is_empty() {
                                        current_input.pop();
                                        cursor_pos = current_input.chars().count();

                                        // Перерисовываем строку
                                        execute!(io::stdout(),
                                            cursor::MoveToColumn(0),
                                            terminal::Clear(ClearType::CurrentLine)
                                        ).unwrap();
                                        print!("> {}", current_input);

                                        io::stdout().flush().unwrap();
                                    }
                                }
                                KeyEvent {
                                    code: KeyCode::Left,
                                    ..
                                } => {
                                    // Стрелка влево
                                    if cursor_pos > 0 {
                                        cursor_pos -= 1;
                                        execute!(io::stdout(), cursor::MoveLeft(1)).unwrap();
                                        io::stdout().flush().unwrap();
                                    }
                                }
                                KeyEvent {
                                    code: KeyCode::Right,
                                    ..
                                } => {
                                    // Стрелка вправо
                                    if cursor_pos < current_input.chars().count() {
                                        cursor_pos += 1;
                                        execute!(io::stdout(), cursor::MoveRight(1)).unwrap();
                                        io::stdout().flush().unwrap();
                                    }
                                }
                                KeyEvent {
                                    code: KeyCode::Char(c),
                                    ..
                                } => {
                                    // Обычный символ - добавляем в конец строки для простоты
                                    current_input.push(c);
                                    cursor_pos = current_input.chars().count();

                                    // Перерисовываем строку
                                    execute!(io::stdout(),
                                        cursor::MoveToColumn(0),
                                        terminal::Clear(ClearType::CurrentLine)
                                    ).unwrap();
                                    print!("> {}", current_input);

                                    io::stdout().flush().unwrap();
                                }
                                _ => {}
                            }
                        }
                    }
                }
            }
        }
    });

    // Ждем завершения любой из задач
    let result = tokio::select! {
        _ = notification_task => Ok(()),
        _ = send_task => Ok(()),
        _ = interface_task => Ok(()),
    };

    // Восстанавливаем нормальный режим терминала
    terminal::disable_raw_mode()?;
    println!("\nВыход из терминала...");

    result
}

// Функция для преобразования байтов в hex строку
fn hex_string(bytes: &[u8]) -> String {
    bytes
        .iter()
        .map(|b| format!("{:02x}", b))
        .collect::<Vec<_>>()
        .join(" ")
}



// Функция для обработки и вывода цветного текста с ANSI кодами
pub fn print_colored_string(bytes: &[u8]) {
    let text = String::from_utf8_lossy(bytes);

    // Регулярное выражение для поиска ANSI escape последовательностей
    let ansi_regex = Regex::new(r"\x1B\[([0-9;]*)m").unwrap();

    let mut last_end = 0;
    let mut current_color: Option<Color> = None;

    for mat in ansi_regex.find_iter(&text) {
        // Выводим текст до ANSI кода
        let before_ansi = &text[last_end..mat.start()];
        if !before_ansi.is_empty() {
            if let Some(color) = current_color {
                print!("{}", before_ansi.color(color));
            } else {
                print!("{}", before_ansi);
            }
        }

        // Парсим ANSI код
        let ansi_code = &text[mat.start()..mat.end()];
        current_color = parse_ansi_color(ansi_code);

        last_end = mat.end();
    }

    // Выводим оставшийся текст
    let remaining = &text[last_end..];
    if !remaining.is_empty() {
        if let Some(color) = current_color {
            print!("{}", remaining.color(color));
        } else {
            print!("{}", remaining);
        }
    }

    // Добавляем перенос строки, если его нет
    if !text.ends_with('\n') {
        println!();
    }
}

// Функция для парсинга ANSI цветных кодов
pub fn parse_ansi_color(ansi_code: &str) -> Option<Color> {
    // Извлекаем числовые коды из ANSI последовательности
    let codes_str = ansi_code.trim_start_matches("\x1B[").trim_end_matches('m');

    if codes_str.is_empty() || codes_str == "0" {
        return None; // Сброс цвета
    }

    // Парсим коды, разделенные точкой с запятой
    let codes: Vec<u8> = codes_str
        .split(';')
        .filter_map(|s| s.parse().ok())
        .collect();

    // Ищем цветовые коды в последовательности
    for &code in &codes {
        match code {
            // Стандартные цвета (30-37)
            30 => return Some(Color::Black),
            31 => return Some(Color::Red),
            32 => return Some(Color::Green),
            33 => return Some(Color::Yellow),
            34 => return Some(Color::Blue),
            35 => return Some(Color::Magenta),
            36 => return Some(Color::Cyan),
            37 => return Some(Color::White),

            // Яркие цвета (90-97)
            90 => return Some(Color::BrightBlack),
            91 => return Some(Color::BrightRed),
            92 => return Some(Color::BrightGreen),
            93 => return Some(Color::BrightYellow),
            94 => return Some(Color::BrightBlue),
            95 => return Some(Color::BrightMagenta),
            96 => return Some(Color::BrightCyan),
            97 => return Some(Color::BrightWhite),

            // Игнорируем другие коды (жирный, курсив и т.д.)
            _ => continue,
        }
    }

    None
}
