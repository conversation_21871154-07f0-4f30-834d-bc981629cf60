// Демонстрация работы парсера ANSI цветных кодов
use bsh_ble_terminal::{print_colored_string, parse_ansi_color};

fn main() {
    println!("=== Демонстрация парсера ANSI цветных кодов ===\n");
    
    // Тест с примером из ESP32
    println!("1. Пример ESP32 лога:");
    let esp32_log = b".\x1B[0;32mI (170974) -queues hlp-: sending uart cmd: == heartbeat ==\x1B[0m.";
    print_colored_string(esp32_log);
    
    println!("\n2. Различные цвета:");
    let test_cases = vec![
        ("\x1B[31mError: Connection failed\x1B[0m", "Ошибка"),
        ("\x1B[33mWarning: Low battery\x1B[0m", "Предупреждение"),
        ("\x1B[32mSuccess: Data received\x1B[0m", "Успех"),
        ("\x1B[34mInfo: Processing...\x1B[0m", "Информация"),
        ("\x1B[35mDebug: Variable value = 42\x1B[0m", "Отладка"),
        ("\x1B[36mTrace: Function called\x1B[0m", "Трассировка"),
    ];
    
    for (data, description) in test_cases {
        print!("  {}: ", description);
        print_colored_string(data.as_bytes());
    }
    
    println!("\n3. Яркие цвета:");
    let bright_cases = vec![
        ("\x1B[91mBright Red Alert!\x1B[0m", "Яркий красный"),
        ("\x1B[92mBright Green OK\x1B[0m", "Яркий зеленый"),
        ("\x1B[93mBright Yellow Warning\x1B[0m", "Яркий желтый"),
        ("\x1B[94mBright Blue Info\x1B[0m", "Яркий синий"),
        ("\x1B[95mBright Magenta Debug\x1B[0m", "Яркая магента"),
        ("\x1B[96mBright Cyan Trace\x1B[0m", "Яркий голубой"),
    ];
    
    for (data, description) in bright_cases {
        print!("  {}: ", description);
        print_colored_string(data.as_bytes());
    }
    
    println!("\n4. Комбинированные коды:");
    let combined_data = b"\x1B[0;31mERROR\x1B[0m: \x1B[1;33mConnection timeout\x1B[0m after \x1B[32m30 seconds\x1B[0m";
    print_colored_string(combined_data);
    
    println!("\n5. Многострочный текст:");
    let multiline = b"\x1B[32mStarting application...\x1B[0m\n\x1B[33mLoading configuration...\x1B[0m\n\x1B[31mError in config file!\x1B[0m\n\x1B[32mUsing default settings\x1B[0m";
    print_colored_string(multiline);
    
    println!("\n=== Демонстрация завершена ===");
}
