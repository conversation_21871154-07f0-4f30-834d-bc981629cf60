{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 4544263003301826467, "deps": [[5103565458935487, "futures_io", false, 16304259299962568516], [1811549171721445101, "futures_channel", false, 13892643310377721832], [7013762810557009322, "futures_sink", false, 17656788375281377711], [7620660491849607393, "futures_core", false, 2686345069947063909], [10629569228670356391, "futures_util", false, 227721168348072034], [12779779637805422465, "futures_executor", false, 331229616394995516], [16240732885093539806, "futures_task", false, 15296668597556637616]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-86ce4fdbcabe3e33\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}