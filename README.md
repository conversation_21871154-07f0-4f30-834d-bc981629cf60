# BLE Terminal с поддержкой ANSI цветов

Приложение для работы с BLE устройствами через терминал, написанное на Rust с использованием библиотеки `btleplug`. Поддерживает автоматическое распознавание и отображение ANSI цветных кодов.

## Функциональность

- **Сканирование BLE устройств** в течение 10 секунд
- **Интерактивный выбор устройства** для подключения
- **Подключение к выбранному устройству**
- **Подписка на характеристику** `0000F307-0000-1000-8000-00805f9b34fb` в сервисе `000000F3-0000-1000-8000-00805f9b34fb`
- **🎨 Парсинг и отображение ANSI цветных кодов** - автоматическое распознавание цветных маркеров в данных и вывод текста с соответствующими цветами
- **Поддержка стандартных цветов** (30-37): черный, красный, зеленый, желтый, синий, магента, голубой, белый
- **Поддержка ярких цветов** (90-97): яркие версии всех стандартных цветов
- **Обработка комбинированных ANSI кодов** (например, `\x1B[0;32m`)

## Пример работы с цветными данными

Приложение автоматически распознает и отображает цветные ANSI коды, такие как:
```
.\x1B[0;32mI (170974) -queues hlp-: sending uart cmd: == heartbeat ==\x1B[0m.
```

Этот текст будет отображен зеленым цветом в консоли.

## Требования

- Rust 1.70 или новее
- BLE адаптер (встроенный или USB)
- Операционная система с поддержкой BLE (Windows, macOS, Linux)

## Установка и запуск

1. Клонируйте репозиторий или скопируйте файлы проекта
2. Убедитесь, что у вас установлен Rust: https://rustup.rs/
3. Перейдите в директорию проекта
4. Запустите приложение:

```bash
cargo run
```

## Демонстрация парсера ANSI кодов

Для демонстрации работы парсера цветных кодов запустите:

```bash
cargo run --example demo_ansi
```

## Использование

1. Запустите приложение
2. Дождитесь завершения сканирования (10 секунд)
3. Выберите устройство из списка, введя его номер
4. Дождитесь подключения к устройству
5. Приложение автоматически найдет нужный сервис и характеристику
6. Все данные, полученные через характеристику, будут выводиться в консоль с поддержкой цветов
7. Для выхода нажмите Ctrl+C

## Поддерживаемые ANSI цвета

### Стандартные цвета (30-37):
- `\x1B[30m` - Черный
- `\x1B[31m` - Красный
- `\x1B[32m` - Зеленый
- `\x1B[33m` - Желтый
- `\x1B[34m` - Синий
- `\x1B[35m` - Магента
- `\x1B[36m` - Голубой
- `\x1B[37m` - Белый

### Яркие цвета (90-97):
- `\x1B[90m` - Яркий черный (серый)
- `\x1B[91m` - Яркий красный
- `\x1B[92m` - Яркий зеленый
- `\x1B[93m` - Яркий желтый
- `\x1B[94m` - Яркий синий
- `\x1B[95m` - Яркая магента
- `\x1B[96m` - Яркий голубой
- `\x1B[97m` - Яркий белый

### Сброс цвета:
- `\x1B[0m` - Сброс всех атрибутов

## Зависимости

- `btleplug`: библиотека для работы с BLE
- `tokio`: асинхронная среда выполнения
- `futures`: утилиты для работы с асинхронным кодом
- `uuid`: работа с UUID
- `regex`: парсинг ANSI escape последовательностей
- `colored`: отображение цветного текста в консоли

## Структура проекта

```
bsh_ble_terminal/
├── Cargo.toml              # Конфигурация проекта и зависимости
├── README.md               # Этот файл
├── EXAMPLES.md             # Примеры использования
├── src/
│   ├── main.rs             # Основной код приложения
│   ├── lib.rs              # Библиотечные функции
│   └── test_ansi.rs        # Тесты для парсера ANSI кодов
└── examples/
    └── demo_ansi.rs        # Демонстрация парсера цветов
```

## Тестирование

Запустите тесты для проверки работы парсера ANSI кодов:

```bash
cargo test
```

## Устранение неполадок

### Устройства не найдены
- Убедитесь, что BLE адаптер включен
- Проверьте, что целевое устройство находится в режиме обнаружения
- Увеличьте время сканирования в коде, если необходимо

### Ошибка подключения
- Убедитесь, что устройство поддерживает подключение
- Проверьте, что устройство не подключено к другому клиенту
- Попробуйте перезапустить приложение

### Сервис или характеристика не найдены
- Убедитесь, что UUID сервиса и характеристики указаны правильно
- Проверьте документацию вашего BLE устройства
- Используйте BLE сканер для проверки доступных сервисов

### Цвета не отображаются
- Убедитесь, что ваш терминал поддерживает ANSI цвета
- Попробуйте запустить демонстрацию: `cargo run --example demo_ansi`
- В Windows используйте современный терминал (Windows Terminal, PowerShell Core)

## Примеры цветного вывода

Типичные примеры данных от ESP32 устройств:
- `\x1B[0;32mI (12345) TAG: Info message\x1B[0m` - Информационное сообщение (зеленый)
- `\x1B[0;33mW (12345) TAG: Warning message\x1B[0m` - Предупреждение (желтый)
- `\x1B[0;31mE (12345) TAG: Error message\x1B[0m` - Ошибка (красный)

## Лицензия

MIT License
