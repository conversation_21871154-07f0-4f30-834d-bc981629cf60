# BLE Terminal

Приложение для работы с BLE устройствами через терминал, написанное на Rust с использованием библиотеки `btleplug`.

## Функциональность

- Сканирование BLE устройств в течение 10 секунд
- Интерактивный выбор устройства для подключения
- Подключение к выбранному устройству
- Подписка на характеристику `0000F308-0000-1000-8000-00805f9b34fb` в сервисе `000000F3-0000-1000-8000-00805f9b34fb`
- Вывод всех полученных данных в консоль в различных форматах (Hex, ASCII, Bytes)

## Требования

- Rust 1.70 или новее
- BLE адаптер (встроенный или USB)
- Операционная система с поддержкой BLE (Windows, macOS, Linux)

## Установка и запуск

1. Клонируйте репозиторий или скопируйте файлы проекта
2. Убедитесь, что у вас установлен Rust: https://rustup.rs/
3. Перейдите в директорию проекта
4. Запустите приложение:

```bash
cargo run
```

## Использование

1. Запустите приложение
2. Дождитесь завершения сканирования (10 секунд)
3. Выберите устройство из списка, введя его номер
4. Дождитесь подключения к устройству
5. Приложение автоматически найдет нужный сервис и характеристику
6. Все данные, полученные через характеристику, будут выводиться в консоль
7. Для выхода нажмите Ctrl+C

## Формат вывода данных

Для каждого полученного пакета данных выводится:
- **Hex**: данные в шестнадцатеричном формате
- **ASCII**: данные в ASCII формате (непечатаемые символы заменяются на '.')
- **Bytes**: сырые байты в виде массива

## Зависимости

- `btleplug`: библиотека для работы с BLE
- `tokio`: асинхронная среда выполнения
- `futures`: утилиты для работы с асинхронным кодом
- `uuid`: работа с UUID

## Структура проекта

```
bsh_ble_terminal/
├── Cargo.toml          # Конфигурация проекта и зависимости
├── README.md           # Этот файл
└── src/
    └── main.rs         # Основной код приложения
```

## Устранение неполадок

### Устройства не найдены
- Убедитесь, что BLE адаптер включен
- Проверьте, что целевое устройство находится в режиме обнаружения
- Увеличьте время сканирования в коде, если необходимо

### Ошибка подключения
- Убедитесь, что устройство поддерживает подключение
- Проверьте, что устройство не подключено к другому клиенту
- Попробуйте перезапустить приложение

### Сервис или характеристика не найдены
- Убедитесь, что UUID сервиса и характеристики указаны правильно
- Проверьте документацию вашего BLE устройства
- Используйте BLE сканер для проверки доступных сервисов

## Лицензия

MIT License
