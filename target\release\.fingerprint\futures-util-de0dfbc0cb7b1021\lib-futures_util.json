{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 7200522559614294938, "deps": [[5103565458935487, "futures_io", false, 16304259299962568516], [1615478164327904835, "pin_utils", false, 11024910669699445016], [1811549171721445101, "futures_channel", false, 13892643310377721832], [1906322745568073236, "pin_project_lite", false, 10525523116055432053], [5451793922601807560, "slab", false, 3962062320171320065], [7013762810557009322, "futures_sink", false, 17656788375281377711], [7620660491849607393, "futures_core", false, 2686345069947063909], [10565019901765856648, "futures_macro", false, 15948369268763910863], [15932120279885307830, "memchr", false, 1841256086569912888], [16240732885093539806, "futures_task", false, 15296668597556637616]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-de0dfbc0cb7b1021\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}