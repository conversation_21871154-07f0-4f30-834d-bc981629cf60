// Тестовый модуль для проверки парсинга ANSI кодов
use crate::{print_colored_string, parse_ansi_color};
use colored::Color;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_ansi_color() {
        // Тест зеленого цвета
        assert_eq!(parse_ansi_color("\x1B[32m"), Some(Color::Green));

        // Тест красного цвета
        assert_eq!(parse_ansi_color("\x1B[31m"), Some(Color::Red));

        // Тест сброса цвета
        assert_eq!(parse_ansi_color("\x1B[0m"), None);

        // Тест комбинированного кода
        assert_eq!(parse_ansi_color("\x1B[0;32m"), Some(Color::Green));

        // Тест яркого синего
        assert_eq!(parse_ansi_color("\x1B[94m"), Some(Color::BrightBlue));
    }

    #[test]
    fn test_print_colored_string() {
        // Тестовые данные как в примере
        let test_data = b".\x1B[0;32mI (170974) -queues hlp-: sending uart cmd: == heartbeat ==\x1B[0m.";

        println!("Тест цветного вывода:");
        print_colored_string(test_data);

        // Тест с разными цветами
        let test_data2 = b"\x1B[31mError:\x1B[0m \x1B[33mWarning message\x1B[0m \x1B[32mSuccess!\x1B[0m";
        print_colored_string(test_data2);
    }
}

// Функция для демонстрации различных цветов
pub fn demo_colors() {
    println!("Демонстрация цветов ANSI:");

    let test_cases = vec![
        ("\x1B[31mRed text\x1B[0m", "Красный"),
        ("\x1B[32mGreen text\x1B[0m", "Зеленый"),
        ("\x1B[33mYellow text\x1B[0m", "Желтый"),
        ("\x1B[34mBlue text\x1B[0m", "Синий"),
        ("\x1B[35mMagenta text\x1B[0m", "Магента"),
        ("\x1B[36mCyan text\x1B[0m", "Голубой"),
        ("\x1B[91mBright red\x1B[0m", "Яркий красный"),
        ("\x1B[92mBright green\x1B[0m", "Яркий зеленый"),
        ("\x1B[0;32mI (170974) ESP32: Heartbeat\x1B[0m", "ESP32 лог"),
    ];

    for (data, description) in test_cases {
        print!("{}: ", description);
        print_colored_string(data.as_bytes());
    }
}
