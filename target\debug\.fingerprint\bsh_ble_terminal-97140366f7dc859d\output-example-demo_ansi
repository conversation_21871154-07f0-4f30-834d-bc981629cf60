{"$message_type":"diagnostic","message":"unused import: `parse_ansi_color`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"examples\\demo_ansi.rs","byte_start":132,"byte_end":148,"line_start":2,"line_end":2,"column_start":46,"column_end":62,"is_primary":true,"text":[{"text":"use bsh_ble_terminal::{print_colored_string, parse_ansi_color};","highlight_start":46,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"examples\\demo_ansi.rs","byte_start":130,"byte_end":148,"line_start":2,"line_end":2,"column_start":44,"column_end":62,"is_primary":true,"text":[{"text":"use bsh_ble_terminal::{print_colored_string, parse_ansi_color};","highlight_start":44,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples\\demo_ansi.rs","byte_start":109,"byte_end":110,"line_start":2,"line_end":2,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use bsh_ble_terminal::{print_colored_string, parse_ansi_color};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples\\demo_ansi.rs","byte_start":148,"byte_end":149,"line_start":2,"line_end":2,"column_start":62,"column_end":63,"is_primary":true,"text":[{"text":"use bsh_ble_terminal::{print_colored_string, parse_ansi_color};","highlight_start":62,"highlight_end":63}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `parse_ansi_color`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\demo_ansi.rs:2:46\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bsh_ble_terminal::{print_colored_string, parse_ansi_color};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 1 warning emitted\u001b[0m\n\n"}
