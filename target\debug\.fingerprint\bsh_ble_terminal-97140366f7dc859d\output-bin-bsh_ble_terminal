{"$message_type":"diagnostic","message":"hard linking files in the incremental compilation cache failed. copying files instead. consider moving the cache directory to a file system which supports hard linking in session dir `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working`","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: hard linking files in the incremental compilation cache failed. copying files instead. consider moving the cache directory to a file system which supports hard linking in session dir `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `hex_string` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":12889,"byte_end":12899,"line_start":303,"line_end":303,"column_start":4,"column_end":14,"is_primary":true,"text":[{"text":"fn hex_string(bytes: &[u8]) -> String {","highlight_start":4,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `hex_string` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:303:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn hex_string(bytes: &[u8]) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal.9tyug1j7i2n3gzlsxnxz3077b.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working\\9tyug1j7i2n3gzlsxnxz3077b.o`: Отказано в доступе. (os error 5)","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal.9tyug1j7i2n3gzlsxnxz3077b.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working\\9tyug1j7i2n3gzlsxnxz3077b.o`: Отказано в доступе. (os error 5)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal.9wr5t95k5bs4ecsd5ozev7ofq.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working\\9wr5t95k5bs4ecsd5ozev7ofq.o`: Отказано в доступе. (os error 5)","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal.9wr5t95k5bs4ecsd5ozev7ofq.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working\\9wr5t95k5bs4ecsd5ozev7ofq.o`: Отказано в доступе. (os error 5)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error finalizing incremental compilation session directory `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working`: Отказано в доступе. (os error 5)","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error finalizing incremental compilation session directory `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-0qxokshffuja2\\s-h9qb0ep2jy-18ey66b-working`: Отказано в доступе. (os error 5)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"5 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 5 warnings emitted\u001b[0m\n\n"}
