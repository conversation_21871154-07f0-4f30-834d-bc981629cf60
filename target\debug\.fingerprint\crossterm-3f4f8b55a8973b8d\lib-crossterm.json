{"rustc": 12488743700189009532, "features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"events\", \"windows\"]", "declared_features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"event-stream\", \"events\", \"filedescriptor\", \"libc\", \"osc52\", \"serde\", \"use-dev-tty\", \"windows\"]", "target": 7162149947039624270, "profile": 2241668132362809309, "path": 11186238673004890830, "deps": [[4495526598637097934, "parking_lot", false, 2048661303185111376], [7896293946984509699, "bitflags", false, 10339589823116915222], [10020888071089587331, "<PERSON>ap<PERSON>", false, 2053867106870981483], [11293676373856528358, "derive_more", false, 17985552711997769498], [11763018104473073732, "document_features", false, 6967180001657141558], [17658759660230624279, "crossterm_winapi", false, 17664981358774097528]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crossterm-3f4f8b55a8973b8d\\dep-lib-crossterm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}